{"name": "next-meeting-scheduler-backend", "version": "1.0.0", "description": "Backend API for Next Meeting Scheduler", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "googleapis": "^128.0.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "express-session": "^1.17.3", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.4", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/passport": "^1.0.16", "@types/passport-google-oauth20": "^2.0.14", "@types/express-session": "^1.17.10", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "@types/jest": "^29.5.8", "jest": "^29.7.0", "ts-jest": "^29.1.1"}}