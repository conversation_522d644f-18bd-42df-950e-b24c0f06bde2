import cron from 'node-cron';
import Meeting from '../models/Meeting';
import emailService from './emailService';

class ReminderService {
  private isRunning = false;

  start() {
    if (this.isRunning) {
      console.log('Reminder service is already running');
      return;
    }

    console.log('🔔 Starting reminder service...');
    this.isRunning = true;

    // Check for reminders every 5 minutes
    cron.schedule('*/5 * * * *', async () => {
      await this.checkAndSendReminders();
    });

    console.log('✅ Reminder service started');
  }

  stop() {
    this.isRunning = false;
    console.log('🔕 Reminder service stopped');
  }

  private async checkAndSendReminders() {
    try {
      const now = new Date();
      const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);
      const twentyFourHoursFromNow = new Date(now.getTime() + 24 * 60 * 60 * 1000);

      // Find meetings that need 1-hour reminders
      const oneHourReminders = await Meeting.find({
        startTime: {
          $gte: now,
          $lte: oneHourFromNow,
        },
        status: 'scheduled',
        'reminders.oneHour.sent': false,
      }).populate('organizer', 'name email');

      // Find meetings that need 24-hour reminders
      const twentyFourHourReminders = await Meeting.find({
        startTime: {
          $gte: now,
          $lte: twentyFourHoursFromNow,
        },
        status: 'scheduled',
        'reminders.twentyFourHour.sent': false,
      }).populate('organizer', 'name email');

      // Send 1-hour reminders
      for (const meeting of oneHourReminders) {
        try {
          await emailService.sendMeetingReminder(meeting, '1hour');
          
          // Mark as sent
          meeting.reminders.oneHour.sent = true;
          meeting.reminders.oneHour.sentAt = new Date();
          await meeting.save();
          
          console.log(`✅ 1-hour reminder sent for meeting: ${meeting.title}`);
        } catch (error) {
          console.error(`❌ Failed to send 1-hour reminder for meeting ${meeting.title}:`, error);
        }
      }

      // Send 24-hour reminders
      for (const meeting of twentyFourHourReminders) {
        try {
          await emailService.sendMeetingReminder(meeting, '24hour');
          
          // Mark as sent
          meeting.reminders.twentyFourHour.sent = true;
          meeting.reminders.twentyFourHour.sentAt = new Date();
          await meeting.save();
          
          console.log(`✅ 24-hour reminder sent for meeting: ${meeting.title}`);
        } catch (error) {
          console.error(`❌ Failed to send 24-hour reminder for meeting ${meeting.title}:`, error);
        }
      }

      if (oneHourReminders.length > 0 || twentyFourHourReminders.length > 0) {
        console.log(`📧 Processed ${oneHourReminders.length} 1-hour and ${twentyFourHourReminders.length} 24-hour reminders`);
      }
    } catch (error) {
      console.error('❌ Error in reminder service:', error);
    }
  }

  // Manual trigger for testing
  async triggerReminders() {
    console.log('🔔 Manually triggering reminder check...');
    await this.checkAndSendReminders();
  }
}

export default new ReminderService();
