import React from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  AppBar,
  Toolbar,
  Typography,
  Button,
  Avatar,
  Menu,
  MenuItem,
  IconButton,
  Container,
  Divider,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Settings as SettingsIcon,
  Logout as LogoutIcon,
  KeyboardArrowDown as ArrowDownIcon,
} from '@mui/icons-material';
import { useAuth } from '@/store/authStore';
import toast from 'react-hot-toast';

const Layout: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    toast.success('Logged out successfully');
    navigate('/login');
    handleMenuClose();
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    handleMenuClose();
  };

  const isActive = (path: string) => location.pathname === path;

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Header */}
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'background.paper',
          borderBottom: '1px solid',
          borderColor: 'grey.200',
        }}
      >
        <Toolbar sx={{ justifyContent: 'space-between' }}>
          {/* Logo */}
          <Box
            sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
            onClick={() => navigate('/dashboard')}
          >
            <Typography
              variant="h5"
              component="h1"
              sx={{
                fontWeight: 700,
                color: 'primary.main',
                mr: 4,
              }}
            >
              Next Meeting
            </Typography>
          </Box>

          {/* Navigation */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Button
              startIcon={<DashboardIcon />}
              onClick={() => navigate('/dashboard')}
              sx={{
                color: isActive('/dashboard') ? 'primary.main' : 'text.secondary',
                fontWeight: isActive('/dashboard') ? 600 : 400,
                '&:hover': { bgcolor: 'grey.50' },
              }}
            >
              Dashboard
            </Button>

            {/* User Menu */}
            <Button
              onClick={handleMenuOpen}
              endIcon={<ArrowDownIcon />}
              sx={{
                ml: 2,
                color: 'text.primary',
                textTransform: 'none',
                '&:hover': { bgcolor: 'grey.50' },
              }}
            >
              <Avatar
                src={user?.avatar}
                sx={{ width: 32, height: 32, mr: 1 }}
              >
                {user?.name?.charAt(0)}
              </Avatar>
              {user?.name}
            </Button>

            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
              PaperProps={{
                sx: {
                  mt: 1,
                  minWidth: 200,
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'grey.200',
                },
              }}
            >
              <MenuItem onClick={() => handleNavigation('/settings')}>
                <SettingsIcon sx={{ mr: 2, fontSize: 20 }} />
                Settings
              </MenuItem>
              <Divider />
              <MenuItem onClick={handleLogout}>
                <LogoutIcon sx={{ mr: 2, fontSize: 20 }} />
                Logout
              </MenuItem>
            </Menu>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Outlet />
      </Container>
    </Box>
  );
};

export default Layout;
