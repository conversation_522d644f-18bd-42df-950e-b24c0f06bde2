import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Divider,
  Alert,
  Stack,
} from '@mui/material';
import { Save as SaveIcon } from '@mui/icons-material';
import { useMutation, useQueryClient } from 'react-query';
import { useAuth } from '@/store/authStore';
import { usersApi } from '@/utils/api';
import toast from 'react-hot-toast';

const SettingsPage: React.FC = () => {
  const { user, updateUser } = useAuth();
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState({
    name: user?.name || '',
    timezone: user?.timezone || 'UTC',
    preferences: {
      defaultMeetingDuration: user?.preferences?.defaultMeetingDuration || 30,
      workingHours: {
        start: user?.preferences?.workingHours?.start || '09:00',
        end: user?.preferences?.workingHours?.end || '17:00',
      },
      workingDays: user?.preferences?.workingDays || [1, 2, 3, 4, 5],
      bufferTime: user?.preferences?.bufferTime || 15,
    },
  });

  // Update profile mutation
  const updateProfileMutation = useMutation(
    (data: any) => usersApi.updateProfile(data),
    {
      onSuccess: (updatedUser) => {
        updateUser(updatedUser);
        queryClient.invalidateQueries('user');
        toast.success('Settings updated successfully');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.message || 'Failed to update settings');
      },
    }
  );

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof typeof prev],
          [child]: value,
        },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const handleWorkingDayToggle = (day: number) => {
    const currentDays = formData.preferences.workingDays;
    const newDays = currentDays.includes(day)
      ? currentDays.filter(d => d !== day)
      : [...currentDays, day].sort();
    
    handleInputChange('preferences.workingDays', newDays);
  };

  const handleSave = () => {
    updateProfileMutation.mutate(formData);
  };

  const timezones = [
    'UTC',
    'America/New_York',
    'America/Chicago',
    'America/Denver',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Paris',
    'Europe/Berlin',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Asia/Kolkata',
    'Australia/Sydney',
  ];

  const weekDays = [
    { value: 1, label: 'Monday' },
    { value: 2, label: 'Tuesday' },
    { value: 3, label: 'Wednesday' },
    { value: 4, label: 'Thursday' },
    { value: 5, label: 'Friday' },
    { value: 6, label: 'Saturday' },
    { value: 0, label: 'Sunday' },
  ];

  return (
    <Box>
      <Typography variant="h4" fontWeight={600} gutterBottom>
        Settings
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Manage your account preferences and meeting settings
      </Typography>

      <Grid container spacing={4}>
        {/* Profile Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Profile Information
              </Typography>
              
              <Stack spacing={3}>
                <TextField
                  label="Full Name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  fullWidth
                />

                <TextField
                  label="Email"
                  value={user?.email || ''}
                  disabled
                  fullWidth
                  helperText="Email cannot be changed"
                />

                <FormControl fullWidth>
                  <InputLabel>Timezone</InputLabel>
                  <Select
                    value={formData.timezone}
                    label="Timezone"
                    onChange={(e) => handleInputChange('timezone', e.target.value)}
                  >
                    {timezones.map((tz) => (
                      <MenuItem key={tz} value={tz}>
                        {tz}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Meeting Preferences */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Meeting Preferences
              </Typography>
              
              <Stack spacing={3}>
                <FormControl fullWidth>
                  <InputLabel>Default Meeting Duration</InputLabel>
                  <Select
                    value={formData.preferences.defaultMeetingDuration}
                    label="Default Meeting Duration"
                    onChange={(e) => handleInputChange('preferences.defaultMeetingDuration', e.target.value)}
                  >
                    <MenuItem value={15}>15 minutes</MenuItem>
                    <MenuItem value={30}>30 minutes</MenuItem>
                    <MenuItem value={45}>45 minutes</MenuItem>
                    <MenuItem value={60}>1 hour</MenuItem>
                    <MenuItem value={90}>1.5 hours</MenuItem>
                    <MenuItem value={120}>2 hours</MenuItem>
                  </Select>
                </FormControl>

                <FormControl fullWidth>
                  <InputLabel>Buffer Time Between Meetings</InputLabel>
                  <Select
                    value={formData.preferences.bufferTime}
                    label="Buffer Time Between Meetings"
                    onChange={(e) => handleInputChange('preferences.bufferTime', e.target.value)}
                  >
                    <MenuItem value={0}>No buffer</MenuItem>
                    <MenuItem value={5}>5 minutes</MenuItem>
                    <MenuItem value={10}>10 minutes</MenuItem>
                    <MenuItem value={15}>15 minutes</MenuItem>
                    <MenuItem value={30}>30 minutes</MenuItem>
                  </Select>
                </FormControl>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Working Hours */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Working Hours & Days
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    label="Start Time"
                    type="time"
                    value={formData.preferences.workingHours.start}
                    onChange={(e) => handleInputChange('preferences.workingHours.start', e.target.value)}
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    label="End Time"
                    type="time"
                    value={formData.preferences.workingHours.end}
                    onChange={(e) => handleInputChange('preferences.workingHours.end', e.target.value)}
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
              </Grid>

              <Divider sx={{ my: 3 }} />

              <Typography variant="subtitle1" gutterBottom>
                Working Days
              </Typography>
              <Grid container spacing={1}>
                {weekDays.map((day) => (
                  <Grid item xs={6} sm={4} md={3} key={day.value}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={formData.preferences.workingDays.includes(day.value)}
                          onChange={() => handleWorkingDayToggle(day.value)}
                        />
                      }
                      label={day.label}
                    />
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Connected Accounts */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Connected Accounts
              </Typography>
              
              <Alert severity="info" sx={{ mb: 2 }}>
                Your Google Calendar is connected and syncing automatically.
              </Alert>
              
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Box
                  component="img"
                  src="https://developers.google.com/identity/images/g-logo.png"
                  alt="Google"
                  sx={{ width: 24, height: 24 }}
                />
                <Box>
                  <Typography variant="body1" fontWeight={500}>
                    Google Calendar
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Connected as {user?.email}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Save Button */}
      <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          size="large"
          startIcon={<SaveIcon />}
          onClick={handleSave}
          disabled={updateProfileMutation.isLoading}
        >
          {updateProfileMutation.isLoading ? 'Saving...' : 'Save Changes'}
        </Button>
      </Box>
    </Box>
  );
};

export default SettingsPage;
