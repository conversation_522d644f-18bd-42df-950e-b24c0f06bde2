import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  Container,
  Paper,
  Typography,
  Button,
  Alert,
  Stack,
} from '@mui/material';
import { Google as GoogleIcon } from '@mui/icons-material';
import { useAuth } from '@/store/authStore';
import toast from 'react-hot-toast';

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { isAuthenticated } = useAuth();
  const error = searchParams.get('error');

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  useEffect(() => {
    if (error === 'auth_failed') {
      toast.error('Authentication failed. Please try again.');
    }
  }, [error]);

  const handleGoogleLogin = () => {
    const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
    window.location.href = `${API_BASE_URL}/auth/google`;
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        bgcolor: 'background.default',
        backgroundImage: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      }}
    >
      <Container maxWidth="sm">
        <Paper
          elevation={0}
          sx={{
            p: 6,
            borderRadius: 3,
            textAlign: 'center',
            border: '1px solid',
            borderColor: 'grey.200',
          }}
        >
          <Stack spacing={4}>
            {/* Logo/Brand */}
            <Box>
              <Typography
                variant="h3"
                component="h1"
                sx={{
                  fontWeight: 700,
                  color: 'primary.main',
                  mb: 1,
                }}
              >
                Next Meeting
              </Typography>
              <Typography
                variant="h6"
                color="text.secondary"
                sx={{ fontWeight: 400 }}
              >
                Instantly book follow-up meetings
              </Typography>
            </Box>

            {/* Error Alert */}
            {error && (
              <Alert severity="error" sx={{ borderRadius: 2 }}>
                Authentication failed. Please try again.
              </Alert>
            )}

            {/* Features */}
            <Box sx={{ textAlign: 'left' }}>
              <Typography variant="body1" color="text.secondary" gutterBottom>
                ✨ End any meeting and instantly book the next one
              </Typography>
              <Typography variant="body1" color="text.secondary" gutterBottom>
                📅 Sync with Google Calendar automatically
              </Typography>
              <Typography variant="body1" color="text.secondary" gutterBottom>
                🤝 Check availability for all participants
              </Typography>
              <Typography variant="body1" color="text.secondary">
                ⚡ No email chains, no back-and-forth
              </Typography>
            </Box>

            {/* Login Button */}
            <Button
              variant="contained"
              size="large"
              startIcon={<GoogleIcon />}
              onClick={handleGoogleLogin}
              sx={{
                py: 1.5,
                fontSize: '1rem',
                fontWeight: 500,
                borderRadius: 2,
                textTransform: 'none',
                background: 'linear-gradient(45deg, #4285f4 30%, #34a853 90%)',
                '&:hover': {
                  background: 'linear-gradient(45deg, #3367d6 30%, #2e7d32 90%)',
                  transform: 'translateY(-1px)',
                  boxShadow: '0 4px 12px rgba(66, 133, 244, 0.3)',
                },
                transition: 'all 0.2s ease-in-out',
              }}
            >
              Continue with Google
            </Button>

            {/* Footer */}
            <Typography variant="body2" color="text.secondary">
              By continuing, you agree to sync your Google Calendar
            </Typography>
          </Stack>
        </Paper>
      </Container>
    </Box>
  );
};

export default LoginPage;
