import express from 'express';
import jwt from 'jsonwebtoken';
import User from '../models/User';
import Meeting from '../models/Meeting';
import googleCalendarService from '../services/googleCalendar';

const router = express.Router();

// Middleware to authenticate user
const authenticateUser = async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-jwt-secret') as any;
    const user = await User.findById(decoded.userId);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(401).json({ message: 'Invalid token' });
  }
};

// Get all meetings for user
router.get('/', authenticateUser, async (req, res) => {
  try {
    const user = req.user as any;
    const { status, limit = 50, page = 1 } = req.query;

    // Build query
    const query: any = {
      $or: [
        { organizer: user._id },
        { 'participants.email': user.email }
      ]
    };

    if (status) {
      query.status = status;
    }

    const meetings = await Meeting.find(query)
      .populate('organizer', 'name email avatar')
      .sort({ startTime: -1 })
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit));

    // Also fetch from Google Calendar to sync
    try {
      const calendarEvents = await googleCalendarService.getCalendarEvents(user);
      
      // Sync calendar events with database (simplified)
      for (const event of calendarEvents) {
        const existingMeeting = await Meeting.findOne({ googleEventId: event.id });
        
        if (!existingMeeting) {
          const newMeeting = new Meeting({
            googleEventId: event.id,
            title: event.summary,
            description: event.description,
            startTime: new Date(event.start.dateTime),
            endTime: new Date(event.end.dateTime),
            timezone: event.start.timeZone,
            organizer: user._id,
            participants: event.attendees?.map(attendee => ({
              email: attendee.email,
              name: attendee.displayName,
              status: attendee.responseStatus === 'accepted' ? 'accepted' : 'pending'
            })) || [],
            location: event.location,
            meetingLink: event.hangoutLink,
            metadata: {
              createdVia: 'import',
              source: 'google_calendar'
            }
          });
          
          await newMeeting.save();
        }
      }
    } catch (syncError) {
      console.error('Calendar sync error:', syncError);
      // Continue without sync if it fails
    }

    res.json({
      meetings,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: await Meeting.countDocuments(query)
      }
    });
  } catch (error) {
    console.error('Get meetings error:', error);
    res.status(500).json({ message: 'Failed to fetch meetings' });
  }
});

// Get meeting by ID
router.get('/:id', authenticateUser, async (req, res) => {
  try {
    const user = req.user as any;
    const meeting = await Meeting.findOne({
      _id: req.params.id,
      $or: [
        { organizer: user._id },
        { 'participants.email': user.email }
      ]
    }).populate('organizer', 'name email avatar');

    if (!meeting) {
      return res.status(404).json({ message: 'Meeting not found' });
    }

    res.json(meeting);
  } catch (error) {
    console.error('Get meeting error:', error);
    res.status(500).json({ message: 'Failed to fetch meeting' });
  }
});

// Mark meeting as completed
router.patch('/:id/complete', authenticateUser, async (req, res) => {
  try {
    const user = req.user as any;
    const meeting = await Meeting.findOne({
      _id: req.params.id,
      organizer: user._id
    });

    if (!meeting) {
      return res.status(404).json({ message: 'Meeting not found or not authorized' });
    }

    meeting.status = 'completed';
    await meeting.save();

    res.json({ 
      message: 'Meeting marked as completed',
      meeting 
    });
  } catch (error) {
    console.error('Complete meeting error:', error);
    res.status(500).json({ message: 'Failed to complete meeting' });
  }
});

// Check availability for multiple participants
router.post('/check-availability', authenticateUser, async (req, res) => {
  try {
    const { emails, startDate, endDate, duration = 30 } = req.body;

    if (!emails || !Array.isArray(emails) || emails.length === 0) {
      return res.status(400).json({ message: 'Participant emails are required' });
    }

    const start = new Date(startDate);
    const end = new Date(endDate || new Date(start.getTime() + 7 * 24 * 60 * 60 * 1000)); // Default 7 days

    // Get free/busy information
    const busyTimes = await googleCalendarService.getFreeBusy(emails, start, end);

    // Find available slots
    const user = req.user as any;
    const availableSlots = googleCalendarService.findAvailableSlots(
      busyTimes,
      start,
      end,
      duration,
      user.preferences?.workingHours,
      user.preferences?.workingDays
    );

    res.json({
      availableSlots,
      busyTimes,
      searchParams: {
        emails,
        startDate: start,
        endDate: end,
        duration
      }
    });
  } catch (error) {
    console.error('Check availability error:', error);
    res.status(500).json({ message: 'Failed to check availability' });
  }
});

// Book next meeting (follow-up)
router.post('/book-followup', authenticateUser, async (req, res) => {
  try {
    const user = req.user as any;
    const {
      parentMeetingId,
      title,
      description,
      startTime,
      endTime,
      participants,
      location
    } = req.body;

    // Validate parent meeting
    const parentMeeting = await Meeting.findOne({
      _id: parentMeetingId,
      organizer: user._id
    });

    if (!parentMeeting) {
      return res.status(404).json({ message: 'Parent meeting not found' });
    }

    // Create calendar event
    const calendarEvent = await googleCalendarService.createCalendarEvent(user, {
      summary: title,
      description,
      start: new Date(startTime),
      end: new Date(endTime),
      attendees: participants.map((p: any) => p.email),
      location
    });

    // Create meeting record
    const meeting = new Meeting({
      googleEventId: calendarEvent.id,
      title,
      description,
      startTime: new Date(startTime),
      endTime: new Date(endTime),
      timezone: user.timezone || 'UTC',
      organizer: user._id,
      participants: participants.map((p: any) => ({
        email: p.email,
        name: p.name,
        status: 'pending'
      })),
      location,
      meetingLink: calendarEvent.hangoutLink,
      isFollowUp: true,
      parentMeetingId: parentMeeting._id,
      metadata: {
        createdVia: 'followup'
      }
    });

    await meeting.save();

    res.json({
      message: 'Follow-up meeting booked successfully',
      meeting,
      calendarEvent
    });
  } catch (error) {
    console.error('Book follow-up error:', error);
    res.status(500).json({ message: 'Failed to book follow-up meeting' });
  }
});

export default router;
