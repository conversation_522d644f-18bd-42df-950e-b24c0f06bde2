{"name": "next-meeting-scheduler-frontend", "version": "1.0.0", "private": true, "dependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^5.3.3", "web-vitals": "^3.5.0", "@mui/material": "^5.15.1", "@mui/icons-material": "^5.15.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-date-pickers": "^6.18.3", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "date-fns": "^2.30.0", "@types/react-router-dom": "^5.3.3", "react-google-login": "^5.2.2", "@types/react-google-login": "^5.2.6", "react-query": "^3.39.3", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}