import axios from 'axios';
import { User, Meeting, AvailabilityResponse, BookFollowUpRequest } from '@/types';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// Request interceptor to add auth token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authApi = {
  getCurrentUser: async (token: string): Promise<User> => {
    const response = await api.get('/auth/me', {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  },

  updatePreferences: async (preferences: Partial<User['preferences']>): Promise<User> => {
    const response = await api.put('/auth/preferences', { preferences });
    return response.data.user;
  },

  logout: async (): Promise<void> => {
    await api.post('/auth/logout');
  },
};

// Meetings API
export const meetingsApi = {
  getMeetings: async (params?: {
    status?: string;
    limit?: number;
    page?: number;
  }): Promise<{ meetings: Meeting[]; pagination: any }> => {
    const response = await api.get('/meetings', { params });
    return response.data;
  },

  getMeeting: async (id: string): Promise<Meeting> => {
    const response = await api.get(`/meetings/${id}`);
    return response.data;
  },

  completeMeeting: async (id: string): Promise<Meeting> => {
    const response = await api.patch(`/meetings/${id}/complete`);
    return response.data.meeting;
  },

  checkAvailability: async (params: {
    emails: string[];
    startDate: string;
    endDate?: string;
    duration?: number;
  }): Promise<AvailabilityResponse> => {
    const response = await api.post('/meetings/check-availability', params);
    return response.data;
  },

  bookFollowUp: async (data: BookFollowUpRequest): Promise<{
    meeting: Meeting;
    calendarEvent: any;
  }> => {
    const response = await api.post('/meetings/book-followup', data);
    return response.data;
  },
};

// Users API
export const usersApi = {
  getProfile: async (): Promise<User> => {
    const response = await api.get('/users/profile');
    return response.data;
  },

  updateProfile: async (data: {
    name?: string;
    timezone?: string;
    preferences?: Partial<User['preferences']>;
  }): Promise<User> => {
    const response = await api.put('/users/profile', data);
    return response.data.user;
  },
};

export default api;
