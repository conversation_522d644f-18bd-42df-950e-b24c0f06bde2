import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Chip,
  Avatar,
  Stack,
  Divider,
  IconButton,
  Alert,
  Skeleton,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  VideoCall as VideoCallIcon,
  LocationOn as LocationIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { format, isPast, isToday } from 'date-fns';
import { meetingsApi } from '@/utils/api';
import { Meeting } from '@/types';
import NextMeetingModal from '@/components/NextMeetingModal';
import toast from 'react-hot-toast';

const MeetingDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isNextMeetingModalOpen, setIsNextMeetingModalOpen] = React.useState(false);

  // Fetch meeting details
  const { data: meeting, isLoading, error } = useQuery(
    ['meeting', id],
    () => meetingsApi.getMeeting(id!),
    {
      enabled: !!id,
    }
  );

  // Complete meeting mutation
  const completeMeetingMutation = useMutation(
    () => meetingsApi.completeMeeting(id!),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['meeting', id]);
        queryClient.invalidateQueries('meetings');
        toast.success('Meeting marked as completed');
        setIsNextMeetingModalOpen(true);
      },
      onError: () => {
        toast.error('Failed to complete meeting');
      },
    }
  );

  const handleCompleteMeeting = () => {
    completeMeetingMutation.mutate();
  };

  const formatMeetingTime = (meeting: Meeting) => {
    const start = new Date(meeting.startTime);
    const end = new Date(meeting.endTime);
    
    if (isToday(start)) {
      return `Today, ${format(start, 'h:mm a')} - ${format(end, 'h:mm a')}`;
    } else {
      return `${format(start, 'EEEE, MMMM d, yyyy')} • ${format(start, 'h:mm a')} - ${format(end, 'h:mm a')}`;
    }
  };

  const getStatusColor = (meeting: Meeting) => {
    if (meeting.status === 'completed') return 'success';
    if (isPast(new Date(meeting.endTime))) return 'error';
    if (isToday(new Date(meeting.startTime))) return 'warning';
    return 'primary';
  };

  const getStatusLabel = (meeting: Meeting) => {
    if (meeting.status === 'completed') return 'Completed';
    if (isPast(new Date(meeting.endTime))) return 'Missed';
    if (isToday(new Date(meeting.startTime))) return 'Today';
    return 'Scheduled';
  };

  if (isLoading) {
    return (
      <Box>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/dashboard')}
          sx={{ mb: 3 }}
        >
          Back to Dashboard
        </Button>
        
        <Card>
          <CardContent>
            <Skeleton variant="text" width="60%" height={40} />
            <Skeleton variant="text" width="40%" height={24} />
            <Skeleton variant="rectangular" width="100%" height={200} />
          </CardContent>
        </Card>
      </Box>
    );
  }

  if (error || !meeting) {
    return (
      <Box>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/dashboard')}
          sx={{ mb: 3 }}
        >
          Back to Dashboard
        </Button>
        
        <Alert severity="error">
          Meeting not found or failed to load.
        </Alert>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/dashboard')}
        >
          Back to Dashboard
        </Button>
        
        {meeting.status !== 'completed' && !isPast(new Date(meeting.endTime)) && (
          <Button
            variant="contained"
            startIcon={<CheckCircleIcon />}
            onClick={handleCompleteMeeting}
            disabled={completeMeetingMutation.isLoading}
          >
            {completeMeetingMutation.isLoading ? 'Completing...' : 'Mark Complete'}
          </Button>
        )}
      </Box>

      {/* Meeting Details */}
      <Card>
        <CardContent sx={{ p: 4 }}>
          {/* Title and Status */}
          <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 3 }}>
            <Box>
              <Typography variant="h4" fontWeight={600} gutterBottom>
                {meeting.title}
              </Typography>
              <Chip
                label={getStatusLabel(meeting)}
                color={getStatusColor(meeting)}
                sx={{ mb: 2 }}
              />
            </Box>
            
            {meeting.meetingLink && (
              <Button
                variant="outlined"
                startIcon={<VideoCallIcon />}
                onClick={() => window.open(meeting.meetingLink, '_blank')}
              >
                Join Meeting
              </Button>
            )}
          </Box>

          {/* Time and Location */}
          <Stack spacing={2} sx={{ mb: 4 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <ScheduleIcon color="primary" />
              <Typography variant="body1">
                {formatMeetingTime(meeting)}
              </Typography>
            </Box>
            
            {meeting.location && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <LocationIcon color="primary" />
                <Typography variant="body1">
                  {meeting.location}
                </Typography>
              </Box>
            )}
          </Stack>

          {/* Description */}
          {meeting.description && (
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Description
              </Typography>
              <Typography variant="body1" color="text.secondary">
                {meeting.description}
              </Typography>
            </Box>
          )}

          <Divider sx={{ my: 3 }} />

          {/* Participants */}
          <Box>
            <Typography variant="h6" gutterBottom>
              Participants ({meeting.participants.length + 1})
            </Typography>
            
            <Stack spacing={2}>
              {/* Organizer */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar src={meeting.organizer.avatar}>
                  {meeting.organizer.name.charAt(0)}
                </Avatar>
                <Box>
                  <Typography variant="body1" fontWeight={500}>
                    {meeting.organizer.name} (Organizer)
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {meeting.organizer.email}
                  </Typography>
                </Box>
              </Box>
              
              {/* Participants */}
              {meeting.participants.map((participant, index) => (
                <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar>
                    {(participant.name || participant.email).charAt(0)}
                  </Avatar>
                  <Box>
                    <Typography variant="body1" fontWeight={500}>
                      {participant.name || participant.email}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {participant.email}
                    </Typography>
                  </Box>
                  <Chip
                    label={participant.status}
                    size="small"
                    color={participant.status === 'accepted' ? 'success' : 'default'}
                  />
                </Box>
              ))}
            </Stack>
          </Box>

          {/* Follow-up Info */}
          {meeting.isFollowUp && meeting.parentMeetingId && (
            <Box sx={{ mt: 4, p: 2, bgcolor: 'primary.50', borderRadius: 2 }}>
              <Typography variant="body2" color="primary.main">
                📅 This is a follow-up meeting
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Next Meeting Modal */}
      <NextMeetingModal
        open={isNextMeetingModalOpen}
        onClose={() => setIsNextMeetingModalOpen(false)}
        parentMeeting={meeting}
      />
    </Box>
  );
};

export default MeetingDetailsPage;
