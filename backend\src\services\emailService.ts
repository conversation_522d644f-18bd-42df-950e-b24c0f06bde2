import nodemailer from 'nodemailer';
import { IMeeting } from '../models/Meeting';
import { format } from 'date-fns';

class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransporter({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD,
      },
    });
  }

  async sendMeetingReminder(meeting: IMeeting, reminderType: '1hour' | '24hour') {
    try {
      const startTime = new Date(meeting.startTime);
      const endTime = new Date(meeting.endTime);
      
      const subject = `Reminder: ${meeting.title} ${reminderType === '1hour' ? 'in 1 hour' : 'tomorrow'}`;
      
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #006BFF 0%, #4A90FF 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: white; padding: 30px; border: 1px solid #e2e8f0; }
            .meeting-details { background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .button { display: inline-block; background: #006BFF; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
            .footer { text-align: center; padding: 20px; color: #718096; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Meeting Reminder</h1>
              <p>Your meeting is ${reminderType === '1hour' ? 'starting in 1 hour' : 'scheduled for tomorrow'}</p>
            </div>
            
            <div class="content">
              <h2>${meeting.title}</h2>
              
              <div class="meeting-details">
                <p><strong>📅 Date:</strong> ${format(startTime, 'EEEE, MMMM d, yyyy')}</p>
                <p><strong>🕐 Time:</strong> ${format(startTime, 'h:mm a')} - ${format(endTime, 'h:mm a')} ${meeting.timezone}</p>
                ${meeting.location ? `<p><strong>📍 Location:</strong> ${meeting.location}</p>` : ''}
                ${meeting.description ? `<p><strong>📝 Description:</strong> ${meeting.description}</p>` : ''}
              </div>
              
              ${meeting.meetingLink ? `
                <p style="text-align: center; margin: 30px 0;">
                  <a href="${meeting.meetingLink}" class="button">Join Meeting</a>
                </p>
              ` : ''}
              
              <p><strong>Participants:</strong></p>
              <ul>
                ${meeting.participants.map(p => `<li>${p.name || p.email}</li>`).join('')}
              </ul>
            </div>
            
            <div class="footer">
              <p>This reminder was sent by Next Meeting Scheduler</p>
              <p>Need to reschedule? Contact the meeting organizer.</p>
            </div>
          </div>
        </body>
        </html>
      `;

      const recipients = meeting.participants.map(p => p.email);
      
      await this.transporter.sendMail({
        from: `"Next Meeting Scheduler" <${process.env.EMAIL_USER}>`,
        to: recipients.join(', '),
        subject,
        html: htmlContent,
      });

      console.log(`${reminderType} reminder sent for meeting: ${meeting.title}`);
    } catch (error) {
      console.error('Failed to send meeting reminder:', error);
      throw error;
    }
  }

  async sendMeetingInvitation(meeting: IMeeting) {
    try {
      const startTime = new Date(meeting.startTime);
      const endTime = new Date(meeting.endTime);
      
      const subject = `Meeting Invitation: ${meeting.title}`;
      
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: 'Inter', Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #006BFF 0%, #4A90FF 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: white; padding: 30px; border: 1px solid #e2e8f0; }
            .meeting-details { background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .button { display: inline-block; background: #006BFF; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; margin: 0 10px; }
            .button.secondary { background: #718096; }
            .footer { text-align: center; padding: 20px; color: #718096; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>You're Invited!</h1>
              <p>New meeting scheduled</p>
            </div>
            
            <div class="content">
              <h2>${meeting.title}</h2>
              
              <div class="meeting-details">
                <p><strong>📅 Date:</strong> ${format(startTime, 'EEEE, MMMM d, yyyy')}</p>
                <p><strong>🕐 Time:</strong> ${format(startTime, 'h:mm a')} - ${format(endTime, 'h:mm a')} ${meeting.timezone}</p>
                ${meeting.location ? `<p><strong>📍 Location:</strong> ${meeting.location}</p>` : ''}
                ${meeting.description ? `<p><strong>📝 Description:</strong> ${meeting.description}</p>` : ''}
              </div>
              
              ${meeting.meetingLink ? `
                <p style="text-align: center; margin: 30px 0;">
                  <a href="${meeting.meetingLink}" class="button">Join Meeting</a>
                </p>
              ` : ''}
              
              <p><strong>Other Participants:</strong></p>
              <ul>
                ${meeting.participants.filter(p => p.email !== meeting.participants[0]?.email).map(p => `<li>${p.name || p.email}</li>`).join('')}
              </ul>
              
              <p style="text-align: center; margin: 30px 0;">
                <a href="#" class="button">Accept</a>
                <a href="#" class="button secondary">Decline</a>
              </p>
            </div>
            
            <div class="footer">
              <p>This invitation was sent by Next Meeting Scheduler</p>
              <p>The meeting has been added to your Google Calendar</p>
            </div>
          </div>
        </body>
        </html>
      `;

      const recipients = meeting.participants.map(p => p.email);
      
      await this.transporter.sendMail({
        from: `"Next Meeting Scheduler" <${process.env.EMAIL_USER}>`,
        to: recipients.join(', '),
        subject,
        html: htmlContent,
      });

      console.log(`Meeting invitation sent for: ${meeting.title}`);
    } catch (error) {
      console.error('Failed to send meeting invitation:', error);
      throw error;
    }
  }
}

export default new EmailService();
