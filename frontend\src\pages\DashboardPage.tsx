import React, { useState } from 'react';
import {
  Box,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Avatar,
  Stack,
  IconButton,
  Skeleton,
  Alert,
} from '@mui/material';
import {
  Add as AddIcon,
  VideoCall as VideoCallIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  MoreVert as MoreVertIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { format, isToday, isTomorrow, isPast, isWithinInterval, addDays } from 'date-fns';
import { meetingsApi } from '@/utils/api';
import { Meeting } from '@/types';
import { useAuth } from '@/store/authStore';
import NextMeetingModal from '@/components/NextMeetingModal';
import toast from 'react-hot-toast';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [selectedMeeting, setSelectedMeeting] = useState<Meeting | null>(null);
  const [isNextMeetingModalOpen, setIsNextMeetingModalOpen] = useState(false);

  // Fetch meetings
  const { data: meetingsData, isLoading, error } = useQuery(
    'meetings',
    () => meetingsApi.getMeetings({ limit: 50 }),
    {
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  );

  // Complete meeting mutation
  const completeMeetingMutation = useMutation(
    (meetingId: string) => meetingsApi.completeMeeting(meetingId),
    {
      onSuccess: (updatedMeeting) => {
        queryClient.invalidateQueries('meetings');
        toast.success('Meeting marked as completed');
        setSelectedMeeting(updatedMeeting);
        setIsNextMeetingModalOpen(true);
      },
      onError: () => {
        toast.error('Failed to complete meeting');
      },
    }
  );

  const meetings = meetingsData?.meetings || [];

  // Filter meetings
  const upcomingMeetings = meetings.filter(
    (meeting) => !isPast(new Date(meeting.endTime)) && meeting.status !== 'completed'
  );
  
  const todayMeetings = upcomingMeetings.filter((meeting) =>
    isToday(new Date(meeting.startTime))
  );
  
  const tomorrowMeetings = upcomingMeetings.filter((meeting) =>
    isTomorrow(new Date(meeting.startTime))
  );
  
  const thisWeekMeetings = upcomingMeetings.filter((meeting) =>
    isWithinInterval(new Date(meeting.startTime), {
      start: addDays(new Date(), 2),
      end: addDays(new Date(), 7),
    })
  );

  const recentMeetings = meetings
    .filter((meeting) => meeting.status === 'completed')
    .slice(0, 5);

  const handleCompleteMeeting = (meeting: Meeting) => {
    completeMeetingMutation.mutate(meeting._id);
  };

  const formatMeetingTime = (meeting: Meeting) => {
    const start = new Date(meeting.startTime);
    const end = new Date(meeting.endTime);
    
    if (isToday(start)) {
      return `Today, ${format(start, 'h:mm a')} - ${format(end, 'h:mm a')}`;
    } else if (isTomorrow(start)) {
      return `Tomorrow, ${format(start, 'h:mm a')} - ${format(end, 'h:mm a')}`;
    } else {
      return `${format(start, 'MMM d, h:mm a')} - ${format(end, 'h:mm a')}`;
    }
  };

  const getStatusColor = (meeting: Meeting) => {
    if (meeting.status === 'completed') return 'success';
    if (isPast(new Date(meeting.endTime))) return 'error';
    if (isToday(new Date(meeting.startTime))) return 'warning';
    return 'primary';
  };

  const getStatusLabel = (meeting: Meeting) => {
    if (meeting.status === 'completed') return 'Completed';
    if (isPast(new Date(meeting.endTime))) return 'Missed';
    if (isToday(new Date(meeting.startTime))) return 'Today';
    return 'Scheduled';
  };

  if (isLoading) {
    return (
      <Box>
        <Typography variant="h4" gutterBottom>
          Dashboard
        </Typography>
        <Grid container spacing={3}>
          {[1, 2, 3, 4].map((i) => (
            <Grid item xs={12} md={6} key={i}>
              <Card>
                <CardContent>
                  <Skeleton variant="text" width="60%" height={32} />
                  <Skeleton variant="text" width="40%" height={24} />
                  <Skeleton variant="rectangular" width="100%" height={60} />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <Typography variant="h4" gutterBottom>
          Dashboard
        </Typography>
        <Alert severity="error">
          Failed to load meetings. Please try refreshing the page.
        </Alert>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 600 }}>
          Good {new Date().getHours() < 12 ? 'morning' : new Date().getHours() < 18 ? 'afternoon' : 'evening'}, {user?.name?.split(' ')[0]}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          {upcomingMeetings.length} upcoming meetings
        </Typography>
      </Box>

      <Grid container spacing={4}>
        {/* Today's Meetings */}
        <Grid item xs={12} lg={8}>
          <MeetingSection
            title="Today"
            meetings={todayMeetings}
            onCompleteMeeting={handleCompleteMeeting}
            isLoading={completeMeetingMutation.isLoading}
            emptyMessage="No meetings scheduled for today"
          />
        </Grid>

        {/* Quick Stats */}
        <Grid item xs={12} lg={4}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Stats
              </Typography>
              <Stack spacing={2}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="text.secondary">
                    Today
                  </Typography>
                  <Typography variant="body2" fontWeight={600}>
                    {todayMeetings.length}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="text.secondary">
                    Tomorrow
                  </Typography>
                  <Typography variant="body2" fontWeight={600}>
                    {tomorrowMeetings.length}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="text.secondary">
                    This Week
                  </Typography>
                  <Typography variant="body2" fontWeight={600}>
                    {thisWeekMeetings.length}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Tomorrow's Meetings */}
        <Grid item xs={12} lg={8}>
          <MeetingSection
            title="Tomorrow"
            meetings={tomorrowMeetings}
            onCompleteMeeting={handleCompleteMeeting}
            isLoading={completeMeetingMutation.isLoading}
            emptyMessage="No meetings scheduled for tomorrow"
          />
        </Grid>

        {/* This Week */}
        <Grid item xs={12} lg={8}>
          <MeetingSection
            title="This Week"
            meetings={thisWeekMeetings}
            onCompleteMeeting={handleCompleteMeeting}
            isLoading={completeMeetingMutation.isLoading}
            emptyMessage="No meetings scheduled for this week"
          />
        </Grid>

        {/* Recent Meetings */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Meetings
              </Typography>
              {recentMeetings.length === 0 ? (
                <Typography variant="body2" color="text.secondary">
                  No recent meetings
                </Typography>
              ) : (
                <Stack spacing={2}>
                  {recentMeetings.map((meeting) => (
                    <Box key={meeting._id}>
                      <Typography variant="body2" fontWeight={500} noWrap>
                        {meeting.title}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {format(new Date(meeting.startTime), 'MMM d, h:mm a')}
                      </Typography>
                    </Box>
                  ))}
                </Stack>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Next Meeting Modal */}
      <NextMeetingModal
        open={isNextMeetingModalOpen}
        onClose={() => setIsNextMeetingModalOpen(false)}
        parentMeeting={selectedMeeting}
      />
    </Box>
  );
};

// Meeting Section Component
interface MeetingSectionProps {
  title: string;
  meetings: Meeting[];
  onCompleteMeeting: (meeting: Meeting) => void;
  isLoading: boolean;
  emptyMessage: string;
}

const MeetingSection: React.FC<MeetingSectionProps> = ({
  title,
  meetings,
  onCompleteMeeting,
  isLoading,
  emptyMessage,
}) => {
  const formatMeetingTime = (meeting: Meeting) => {
    const start = new Date(meeting.startTime);
    const end = new Date(meeting.endTime);
    return `${format(start, 'h:mm a')} - ${format(end, 'h:mm a')}`;
  };

  const getStatusColor = (meeting: Meeting) => {
    if (meeting.status === 'completed') return 'success';
    if (isPast(new Date(meeting.endTime))) return 'error';
    if (isToday(new Date(meeting.startTime))) return 'warning';
    return 'primary';
  };

  const getStatusLabel = (meeting: Meeting) => {
    if (meeting.status === 'completed') return 'Completed';
    if (isPast(new Date(meeting.endTime))) return 'Missed';
    if (isToday(new Date(meeting.startTime))) return 'Today';
    return 'Scheduled';
  };

  if (meetings.length === 0) {
    return (
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {emptyMessage}
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
        <Stack spacing={2}>
          {meetings.map((meeting) => (
            <Box
              key={meeting._id}
              sx={{
                p: 2,
                border: '1px solid',
                borderColor: 'grey.200',
                borderRadius: 2,
                '&:hover': { borderColor: 'primary.main' },
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="subtitle1" fontWeight={600} gutterBottom>
                    {meeting.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {formatMeetingTime(meeting)}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <Chip
                      label={getStatusLabel(meeting)}
                      color={getStatusColor(meeting)}
                      size="small"
                    />
                    {meeting.participants.length > 0 && (
                      <Typography variant="caption" color="text.secondary">
                        {meeting.participants.length} participant{meeting.participants.length !== 1 ? 's' : ''}
                      </Typography>
                    )}
                  </Box>
                  {meeting.participants.length > 0 && (
                    <Stack direction="row" spacing={1}>
                      {meeting.participants.slice(0, 3).map((participant, index) => (
                        <Avatar key={index} sx={{ width: 24, height: 24, fontSize: '0.75rem' }}>
                          {participant.name?.charAt(0) || participant.email.charAt(0)}
                        </Avatar>
                      ))}
                      {meeting.participants.length > 3 && (
                        <Typography variant="caption" color="text.secondary">
                          +{meeting.participants.length - 3} more
                        </Typography>
                      )}
                    </Stack>
                  )}
                </Box>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  {meeting.meetingLink && (
                    <IconButton
                      size="small"
                      onClick={() => window.open(meeting.meetingLink, '_blank')}
                    >
                      <VideoCallIcon />
                    </IconButton>
                  )}
                  {meeting.status !== 'completed' && !isPast(new Date(meeting.endTime)) && (
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<CheckCircleIcon />}
                      onClick={() => onCompleteMeeting(meeting)}
                      disabled={isLoading}
                    >
                      Mark Complete
                    </Button>
                  )}
                </Box>
              </Box>
            </Box>
          ))}
        </Stack>
      </CardContent>
    </Card>
  );
};

export default DashboardPage;
