export interface User {
  _id: string;
  googleId: string;
  email: string;
  name: string;
  avatar?: string;
  timezone?: string;
  preferences: {
    defaultMeetingDuration: number;
    workingHours: {
      start: string;
      end: string;
    };
    workingDays: number[];
    bufferTime: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface Participant {
  email: string;
  name?: string;
  status: 'pending' | 'accepted' | 'declined';
}

export interface Meeting {
  _id: string;
  googleEventId?: string;
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  timezone: string;
  organizer: {
    _id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  participants: Participant[];
  location?: string;
  meetingLink?: string;
  status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
  isFollowUp: boolean;
  parentMeetingId?: string;
  reminders: {
    oneHour: {
      sent: boolean;
      sentAt?: string;
    };
    twentyFourHour: {
      sent: boolean;
      sentAt?: string;
    };
  };
  metadata: {
    createdVia: 'manual' | 'followup' | 'import';
    source?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface AvailableSlot {
  start: Date;
  end: Date;
}

export interface FreeBusySlot {
  start: string;
  end: string;
}

export interface AvailabilityResponse {
  availableSlots: AvailableSlot[];
  busyTimes: { [email: string]: FreeBusySlot[] };
  searchParams: {
    emails: string[];
    startDate: string;
    endDate: string;
    duration: number;
  };
}

export interface BookFollowUpRequest {
  parentMeetingId: string;
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  participants: Array<{
    email: string;
    name?: string;
  }>;
  location?: string;
}

export interface ApiResponse<T = any> {
  message?: string;
  data?: T;
  error?: string;
}

export interface PaginationResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
