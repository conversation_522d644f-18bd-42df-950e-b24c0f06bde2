import mongoose, { Document, Schema } from 'mongoose';

export interface IUser extends Document {
  googleId: string;
  email: string;
  name: string;
  avatar?: string;
  accessToken: string;
  refreshToken?: string;
  timezone?: string;
  preferences: {
    defaultMeetingDuration: number;
    workingHours: {
      start: string;
      end: string;
    };
    workingDays: number[];
    bufferTime: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

const UserSchema: Schema = new Schema({
  googleId: {
    type: String,
    required: true,
    unique: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  name: {
    type: String,
    required: true
  },
  avatar: {
    type: String
  },
  accessToken: {
    type: String,
    required: true
  },
  refreshToken: {
    type: String
  },
  timezone: {
    type: String,
    default: 'UTC'
  },
  preferences: {
    defaultMeetingDuration: {
      type: Number,
      default: 30 // minutes
    },
    workingHours: {
      start: {
        type: String,
        default: '09:00'
      },
      end: {
        type: String,
        default: '17:00'
      }
    },
    workingDays: {
      type: [Number],
      default: [1, 2, 3, 4, 5] // Monday to Friday
    },
    bufferTime: {
      type: Number,
      default: 15 // minutes between meetings
    }
  }
}, {
  timestamps: true
});

// Index for faster queries
UserSchema.index({ googleId: 1 });
UserSchema.index({ email: 1 });

export default mongoose.model<IUser>('User', UserSchema);
