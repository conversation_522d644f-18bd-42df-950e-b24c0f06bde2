# Next Meeting Scheduler

A modern web application that allows users to instantly book follow-up meetings right after completing their current meetings. Built with the MERN stack and Google Calendar integration.

## ✨ Features

- **Instant Follow-up Booking**: End any meeting and immediately book the next one
- **Google Calendar Sync**: Automatic synchronization with Google Calendar
- **Smart Availability**: Check availability for all participants automatically
- **Calendly-style UI**: Clean, modern interface inspired by <PERSON><PERSON><PERSON>
- **<PERSON><PERSON> Reminders**: Automated reminders 1 hour and 24 hours before meetings
- **Real-time Updates**: Live dashboard with meeting status updates
- **TypeScript**: Full TypeScript support for better development experience

## 🚀 Quick Start

### Prerequisites

- Node.js 16+ and npm
- MongoDB (local or cloud)
- Google Cloud Console project with Calendar API enabled
- Gmail account for email reminders

### 1. Clone and Install

```bash
git clone <your-repo-url>
cd next-meeting-scheduler
npm run install-deps
```

### 2. Environment Setup

#### Backend Configuration
```bash
cd backend
cp .env.example .env
```

Edit `backend/.env` with your values:
```env
# Server
PORT=5000
NODE_ENV=development
CLIENT_URL=http://localhost:3000

# Database
MONGODB_URI=mongodb://localhost:27017/next-meeting-scheduler

# Secrets
JWT_SECRET=your-super-secret-jwt-key-here
SESSION_SECRET=your-super-secret-session-key-here

# Google OAuth (see setup guide below)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:5000/api/auth/google/callback

# Email
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-gmail-app-password
```

#### Frontend Configuration
```bash
cd frontend
cp .env.example .env
```

Edit `frontend/.env`:
```env
REACT_APP_API_URL=http://localhost:5000/api
```

### 3. Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google Calendar API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Set application type to "Web application"
6. Add authorized redirect URIs:
   - `http://localhost:5000/api/auth/google/callback` (development)
   - `https://your-domain.com/api/auth/google/callback` (production)
7. Copy Client ID and Client Secret to your `.env` file

### 4. Gmail App Password (for email reminders)

1. Enable 2-factor authentication on your Gmail account
2. Go to Google Account settings → Security → App passwords
3. Generate an app password for "Mail"
4. Use this password in `EMAIL_PASSWORD` (not your regular Gmail password)

### 5. Start Development

```bash
# Start both frontend and backend
npm run dev

# Or start individually:
npm run server  # Backend only
npm run client  # Frontend only
```

Visit `http://localhost:3000` to see the application.

## 📱 Usage

### Core Workflow

1. **Login**: Sign in with Google to sync your calendar
2. **Dashboard**: View all your upcoming and past meetings
3. **Complete Meeting**: Click "Mark Complete" on any meeting
4. **Book Next**: Instantly see options to book follow-up:
   - Same time next week
   - Pick from available slots
5. **Confirmation**: Meeting is created in everyone's calendar with automatic invites

### Key Features

- **Smart Scheduling**: Automatically finds times that work for all participants
- **Calendar Sync**: All meetings sync bidirectionally with Google Calendar
- **Email Reminders**: Participants get beautiful email reminders
- **Mobile Responsive**: Works perfectly on all devices

## 🏗️ Architecture

### Backend (Node.js + TypeScript)
- **Express.js**: REST API server
- **MongoDB**: Database with Mongoose ODM
- **Passport.js**: Google OAuth authentication
- **Google APIs**: Calendar integration
- **Nodemailer**: Email reminders
- **Node-cron**: Scheduled reminder jobs

### Frontend (React + TypeScript)
- **React 18**: Modern React with hooks
- **Material-UI**: Calendly-inspired design system
- **React Query**: Server state management
- **React Router**: Client-side routing
- **Zustand**: Authentication state
- **Date-fns**: Date manipulation

## 🚀 Deployment

### Vercel (Frontend)

1. Connect your GitHub repo to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push

### Railway/Render (Backend)

1. Connect your GitHub repo
2. Set environment variables
3. Deploy with automatic builds

### Environment Variables for Production

Update your OAuth redirect URIs and environment variables for production domains.

## 🧪 Testing

```bash
# Backend tests
cd backend
npm test

# Frontend tests
cd frontend
npm test
```

## 📝 API Documentation

### Authentication
- `GET /api/auth/google` - Initiate Google OAuth
- `GET /api/auth/google/callback` - OAuth callback
- `GET /api/auth/me` - Get current user
- `POST /api/auth/logout` - Logout

### Meetings
- `GET /api/meetings` - Get user's meetings
- `GET /api/meetings/:id` - Get meeting details
- `PATCH /api/meetings/:id/complete` - Mark meeting complete
- `POST /api/meetings/check-availability` - Check participant availability
- `POST /api/meetings/book-followup` - Book follow-up meeting

### Users
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For issues and questions:
1. Check the GitHub issues
2. Create a new issue with detailed description
3. Include error logs and environment details

---

**Built with ❤️ for seamless meeting scheduling**
