# Next Meeting Scheduler - Architecture Overview

## 🎯 What This App Does

**Next Meeting Scheduler** solves the common problem of scheduling follow-up meetings. Instead of the usual back-and-forth emails, participants can instantly book their next meeting right after completing the current one.

### Core Value Proposition
- **End any meeting** → **Book next meeting in 2 clicks**
- **No email chains** → **Instant calendar integration**
- **Manual coordination** → **Smart availability checking**

## 🏗️ System Architecture

### Frontend (React + TypeScript)
- **Framework**: React 18 with TypeScript
- **UI Library**: Material-UI with Calendly-inspired design
- **State Management**: 
  - Zustand for authentication state
  - React Query for server state caching
- **Routing**: React Router for SPA navigation
- **Styling**: Material-UI theme with custom Calendly-style components

### Backend (Node.js + TypeScript)
- **Framework**: Express.js with TypeScript
- **Authentication**: Passport.js with Google OAuth 2.0
- **Database**: MongoDB with Mongoose ODM
- **External APIs**: Google Calendar API for meeting management
- **Background Jobs**: Node-cron for automated email reminders
- **Email**: Nodemailer with Gmail SMTP

### Key Services

#### 1. Google Calendar Service
- Fetches user's calendar events
- Creates new calendar events
- Checks participant availability using FreeBusy API
- Handles calendar permissions and token refresh

#### 2. Email Service
- Sends meeting invitations with HTML templates
- Automated reminders (1 hour & 24 hours before meetings)
- Calendly-style email design

#### 3. Reminder Service
- Cron job runs every 5 minutes
- Checks for upcoming meetings needing reminders
- Marks reminders as sent to avoid duplicates

## 📊 Data Flow

### 1. Authentication Flow
```
User → Google OAuth → JWT Token → Protected Routes
```

### 2. Meeting Completion & Follow-up
```
Complete Meeting → Open Modal → Check Availability → Book Meeting → Calendar Event + Email
```

### 3. Availability Checking
```
Participant Emails → Google FreeBusy API → Calculate Free Slots → Display Options
```

### 4. Background Reminders
```
Cron Job → Check Database → Send Emails → Mark as Sent
```

## 🗄️ Database Design

### User Collection
- Stores Google OAuth details and tokens
- User preferences (working hours, default duration)
- Timezone and personal settings

### Meeting Collection
- Meeting details and participants
- Google Calendar event ID for sync
- Follow-up relationship tracking
- Reminder status tracking

## 🔄 User Journey

### Primary Flow: Instant Follow-up Booking

1. **Login**: User authenticates with Google
2. **Dashboard**: View today's meetings synced from Google Calendar
3. **Complete Meeting**: Click "Mark Complete" on any meeting
4. **Modal Opens**: Instant follow-up booking options appear
5. **Choose Option**: 
   - Same time next week (if available)
   - Pick from available time slots
6. **Smart Scheduling**: System checks all participants' availability
7. **Book Meeting**: Enter details and confirm
8. **Automatic Actions**:
   - Creates Google Calendar event for all participants
   - Sends email invitations
   - Schedules automatic reminders
   - Updates dashboard

### Secondary Flows

- **Settings**: Manage working hours, timezone, preferences
- **Meeting Details**: View full meeting information
- **Calendar Sync**: Automatic bidirectional sync with Google Calendar

## 🚀 Key Features

### 1. Smart Availability Checking
- Queries Google Calendar FreeBusy API for all participants
- Calculates mutually available time slots
- Respects working hours and buffer time preferences

### 2. Instant Calendar Integration
- Creates events in all participants' Google Calendars
- Includes Google Meet links automatically
- Bidirectional sync (changes in Google Calendar reflect in app)

### 3. Automated Communication
- Beautiful HTML email invitations
- Reminder emails 24 hours and 1 hour before meetings
- No manual coordination required

### 4. Calendly-style UX
- Clean, modern interface
- Mobile-responsive design
- Intuitive booking flow
- Real-time availability display

## 🔧 Technical Highlights

### Performance Optimizations
- React Query for intelligent caching
- Debounced API calls for availability checking
- Efficient MongoDB queries with proper indexing

### Security Features
- JWT-based authentication
- Rate limiting on API endpoints
- CORS protection
- Helmet.js security headers

### Scalability Considerations
- Stateless backend design
- Horizontal scaling ready
- Background job processing
- Efficient database queries

## 🌐 Deployment Architecture

### Frontend (Vercel)
- Static React build
- CDN distribution
- Environment variable management

### Backend (Railway/Render)
- Node.js container deployment
- MongoDB Atlas integration
- Environment-based configuration

### External Dependencies
- Google Calendar API
- Gmail SMTP
- MongoDB Atlas

## 📈 Future Enhancements

### Potential Features
- Multiple calendar provider support (Outlook, Apple Calendar)
- Team scheduling with resource management
- Advanced recurring meeting patterns
- Integration with video conferencing platforms
- Mobile app development
- Analytics and meeting insights

### Technical Improvements
- Redis caching layer
- WebSocket real-time updates
- Advanced error handling and retry logic
- Comprehensive test suite
- Performance monitoring

## 🎨 Design Philosophy

The app follows Calendly's design principles:
- **Simplicity**: Minimal clicks to achieve goals
- **Clarity**: Clear visual hierarchy and information
- **Efficiency**: Fast, responsive interactions
- **Accessibility**: Works for all users and devices

This architecture ensures the app delivers on its core promise: making follow-up meeting scheduling effortless and instant.
