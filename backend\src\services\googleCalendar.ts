import { google } from 'googleapis';
import { IUser } from '../models/User';

export interface CalendarEvent {
  id: string;
  summary: string;
  description?: string;
  start: {
    dateTime: string;
    timeZone: string;
  };
  end: {
    dateTime: string;
    timeZone: string;
  };
  attendees?: Array<{
    email: string;
    displayName?: string;
    responseStatus: string;
  }>;
  location?: string;
  hangoutLink?: string;
}

export interface FreeBusySlot {
  start: string;
  end: string;
}

export interface AvailableSlot {
  start: Date;
  end: Date;
}

class GoogleCalendarService {
  private getOAuth2Client(user: IUser) {
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_REDIRECT_URI
    );

    oauth2Client.setCredentials({
      access_token: user.accessToken,
      refresh_token: user.refreshToken
    });

    return oauth2Client;
  }

  async getCalendarEvents(user: IUser, timeMin?: Date, timeMax?: Date): Promise<CalendarEvent[]> {
    try {
      const auth = this.getOAuth2Client(user);
      const calendar = google.calendar({ version: 'v3', auth });

      const response = await calendar.events.list({
        calendarId: 'primary',
        timeMin: timeMin?.toISOString() || new Date().toISOString(),
        timeMax: timeMax?.toISOString() || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        singleEvents: true,
        orderBy: 'startTime',
        maxResults: 250
      });

      return response.data.items?.map(event => ({
        id: event.id!,
        summary: event.summary || 'No Title',
        description: event.description,
        start: {
          dateTime: event.start?.dateTime || event.start?.date!,
          timeZone: event.start?.timeZone || user.timezone || 'UTC'
        },
        end: {
          dateTime: event.end?.dateTime || event.end?.date!,
          timeZone: event.end?.timeZone || user.timezone || 'UTC'
        },
        attendees: event.attendees?.map(attendee => ({
          email: attendee.email!,
          displayName: attendee.displayName,
          responseStatus: attendee.responseStatus || 'needsAction'
        })),
        location: event.location,
        hangoutLink: event.hangoutLink
      })) || [];
    } catch (error) {
      console.error('Error fetching calendar events:', error);
      throw new Error('Failed to fetch calendar events');
    }
  }

  async createCalendarEvent(user: IUser, eventData: {
    summary: string;
    description?: string;
    start: Date;
    end: Date;
    attendees: string[];
    location?: string;
  }): Promise<CalendarEvent> {
    try {
      const auth = this.getOAuth2Client(user);
      const calendar = google.calendar({ version: 'v3', auth });

      const event = {
        summary: eventData.summary,
        description: eventData.description,
        start: {
          dateTime: eventData.start.toISOString(),
          timeZone: user.timezone || 'UTC'
        },
        end: {
          dateTime: eventData.end.toISOString(),
          timeZone: user.timezone || 'UTC'
        },
        attendees: eventData.attendees.map(email => ({ email })),
        location: eventData.location,
        conferenceData: {
          createRequest: {
            requestId: `meet-${Date.now()}`,
            conferenceSolutionKey: { type: 'hangoutsMeet' }
          }
        },
        reminders: {
          useDefault: false,
          overrides: [
            { method: 'email', minutes: 24 * 60 },
            { method: 'popup', minutes: 60 }
          ]
        }
      };

      const response = await calendar.events.insert({
        calendarId: 'primary',
        requestBody: event,
        conferenceDataVersion: 1,
        sendUpdates: 'all'
      });

      const createdEvent = response.data;
      return {
        id: createdEvent.id!,
        summary: createdEvent.summary!,
        description: createdEvent.description,
        start: {
          dateTime: createdEvent.start?.dateTime!,
          timeZone: createdEvent.start?.timeZone || user.timezone || 'UTC'
        },
        end: {
          dateTime: createdEvent.end?.dateTime!,
          timeZone: createdEvent.end?.timeZone || user.timezone || 'UTC'
        },
        attendees: createdEvent.attendees?.map(attendee => ({
          email: attendee.email!,
          displayName: attendee.displayName,
          responseStatus: attendee.responseStatus || 'needsAction'
        })),
        location: createdEvent.location,
        hangoutLink: createdEvent.hangoutLink
      };
    } catch (error) {
      console.error('Error creating calendar event:', error);
      throw new Error('Failed to create calendar event');
    }
  }

  async getFreeBusy(emails: string[], timeMin: Date, timeMax: Date): Promise<{ [email: string]: FreeBusySlot[] }> {
    try {
      // For now, we'll use the first user's credentials to check free/busy
      // In a production app, you'd need proper service account or domain-wide delegation
      const calendar = google.calendar({ version: 'v3' });

      const response = await calendar.freebusy.query({
        requestBody: {
          timeMin: timeMin.toISOString(),
          timeMax: timeMax.toISOString(),
          items: emails.map(email => ({ id: email }))
        }
      });

      const result: { [email: string]: FreeBusySlot[] } = {};

      for (const email of emails) {
        const busyTimes = response.data.calendars?.[email]?.busy || [];
        result[email] = busyTimes.map(slot => ({
          start: slot.start!,
          end: slot.end!
        }));
      }

      return result;
    } catch (error) {
      console.error('Error checking free/busy:', error);
      // Return empty busy times if API fails
      const result: { [email: string]: FreeBusySlot[] } = {};
      emails.forEach(email => {
        result[email] = [];
      });
      return result;
    }
  }

  findAvailableSlots(
    busyTimes: { [email: string]: FreeBusySlot[] },
    startDate: Date,
    endDate: Date,
    duration: number, // in minutes
    workingHours: { start: string; end: string } = { start: '09:00', end: '17:00' },
    workingDays: number[] = [1, 2, 3, 4, 5] // Monday to Friday
  ): AvailableSlot[] {
    const slots: AvailableSlot[] = [];
    const current = new Date(startDate);

    while (current < endDate) {
      const dayOfWeek = current.getDay();

      // Skip non-working days
      if (!workingDays.includes(dayOfWeek)) {
        current.setDate(current.getDate() + 1);
        current.setHours(0, 0, 0, 0);
        continue;
      }

      // Set working hours for the day
      const dayStart = new Date(current);
      const [startHour, startMin] = workingHours.start.split(':').map(Number);
      dayStart.setHours(startHour, startMin, 0, 0);

      const dayEnd = new Date(current);
      const [endHour, endMin] = workingHours.end.split(':').map(Number);
      dayEnd.setHours(endHour, endMin, 0, 0);

      // Check each potential slot in the day
      const slotStart = new Date(Math.max(dayStart.getTime(), current.getTime()));

      while (slotStart.getTime() + duration * 60 * 1000 <= dayEnd.getTime()) {
        const slotEnd = new Date(slotStart.getTime() + duration * 60 * 1000);

        // Check if this slot conflicts with any participant's busy time
        let isAvailable = true;

        for (const email in busyTimes) {
          const userBusyTimes = busyTimes[email];

          for (const busySlot of userBusyTimes) {
            const busyStart = new Date(busySlot.start);
            const busyEnd = new Date(busySlot.end);

            // Check for overlap
            if (slotStart < busyEnd && slotEnd > busyStart) {
              isAvailable = false;
              break;
            }
          }

          if (!isAvailable) break;
        }

        if (isAvailable) {
          slots.push({
            start: new Date(slotStart),
            end: new Date(slotEnd)
          });
        }

        // Move to next 15-minute slot
        slotStart.setMinutes(slotStart.getMinutes() + 15);
      }

      // Move to next day
      current.setDate(current.getDate() + 1);
      current.setHours(0, 0, 0, 0);
    }

    return slots.slice(0, 20); // Return first 20 available slots
  }
}

export default new GoogleCalendarService();
