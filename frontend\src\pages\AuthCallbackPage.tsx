import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Box, CircularProgress, Typography } from '@mui/material';
import { useAuth } from '@/store/authStore';
import toast from 'react-hot-toast';

const AuthCallbackPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { login } = useAuth();

  useEffect(() => {
    const handleCallback = async () => {
      const token = searchParams.get('token');
      const error = searchParams.get('error');

      if (error) {
        toast.error('Authentication failed');
        navigate('/login', { replace: true });
        return;
      }

      if (token) {
        try {
          await login(token);
          toast.success('Welcome! Successfully logged in.');
          navigate('/dashboard', { replace: true });
        } catch (error) {
          console.error('Login error:', error);
          toast.error('Failed to complete login');
          navigate('/login', { replace: true });
        }
      } else {
        toast.error('No authentication token received');
        navigate('/login', { replace: true });
      }
    };

    handleCallback();
  }, [searchParams, login, navigate]);

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        bgcolor: 'background.default',
      }}
    >
      <CircularProgress size={48} sx={{ mb: 2 }} />
      <Typography variant="h6" color="text.secondary">
        Completing your login...
      </Typography>
    </Box>
  );
};

export default AuthCallbackPage;
