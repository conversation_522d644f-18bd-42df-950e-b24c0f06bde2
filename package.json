{"name": "next-meeting-scheduler", "version": "1.0.0", "description": "Real-time meeting scheduler with instant follow-up booking", "main": "index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && npm run dev", "client": "cd frontend && npm start", "build": "cd frontend && npm run build", "install-deps": "npm install && cd backend && npm install && cd ../frontend && npm install"}, "keywords": ["meeting", "scheduler", "calendar", "google-calendar"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}