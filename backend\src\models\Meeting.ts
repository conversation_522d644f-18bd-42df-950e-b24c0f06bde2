import mongoose, { Document, Schema } from 'mongoose';

export interface IParticipant {
  email: string;
  name?: string;
  status: 'pending' | 'accepted' | 'declined';
}

export interface IMeeting extends Document {
  googleEventId?: string;
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  timezone: string;
  organizer: mongoose.Types.ObjectId;
  participants: IParticipant[];
  location?: string;
  meetingLink?: string;
  status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled';
  isFollowUp: boolean;
  parentMeetingId?: mongoose.Types.ObjectId;
  reminders: {
    oneHour: {
      sent: boolean;
      sentAt?: Date;
    };
    twentyFourHour: {
      sent: boolean;
      sentAt?: Date;
    };
  };
  metadata: {
    createdVia: 'manual' | 'followup' | 'import';
    source?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const ParticipantSchema: Schema = new Schema({
  email: {
    type: String,
    required: true,
    lowercase: true
  },
  name: {
    type: String
  },
  status: {
    type: String,
    enum: ['pending', 'accepted', 'declined'],
    default: 'pending'
  }
}, { _id: false });

const MeetingSchema: Schema = new Schema({
  googleEventId: {
    type: String,
    sparse: true
  },
  title: {
    type: String,
    required: true
  },
  description: {
    type: String
  },
  startTime: {
    type: Date,
    required: true
  },
  endTime: {
    type: Date,
    required: true
  },
  timezone: {
    type: String,
    required: true,
    default: 'UTC'
  },
  organizer: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  participants: [ParticipantSchema],
  location: {
    type: String
  },
  meetingLink: {
    type: String
  },
  status: {
    type: String,
    enum: ['scheduled', 'ongoing', 'completed', 'cancelled'],
    default: 'scheduled'
  },
  isFollowUp: {
    type: Boolean,
    default: false
  },
  parentMeetingId: {
    type: Schema.Types.ObjectId,
    ref: 'Meeting'
  },
  reminders: {
    oneHour: {
      sent: {
        type: Boolean,
        default: false
      },
      sentAt: Date
    },
    twentyFourHour: {
      sent: {
        type: Boolean,
        default: false
      },
      sentAt: Date
    }
  },
  metadata: {
    createdVia: {
      type: String,
      enum: ['manual', 'followup', 'import'],
      default: 'manual'
    },
    source: String
  }
}, {
  timestamps: true
});

// Indexes for better query performance
MeetingSchema.index({ organizer: 1, startTime: -1 });
MeetingSchema.index({ googleEventId: 1 });
MeetingSchema.index({ startTime: 1, endTime: 1 });
MeetingSchema.index({ 'participants.email': 1 });
MeetingSchema.index({ status: 1 });

export default mongoose.model<IMeeting>('Meeting', MeetingSchema);
