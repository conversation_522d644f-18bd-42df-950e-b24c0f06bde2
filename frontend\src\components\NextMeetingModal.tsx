import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  TextField,
  Chip,
  Grid,
  Card,
  CardContent,
  IconButton,
  Stack,
  Divider,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Close as CloseIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
} from '@mui/icons-material';
import { format, addWeeks, addDays } from 'date-fns';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { meetingsApi } from '@/utils/api';
import { Meeting, AvailableSlot } from '@/types';
import { useAuth } from '@/store/authStore';
import toast from 'react-hot-toast';

interface NextMeetingModalProps {
  open: boolean;
  onClose: () => void;
  parentMeeting: Meeting | null;
}

const NextMeetingModal: React.FC<NextMeetingModalProps> = ({
  open,
  onClose,
  parentMeeting,
}) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [step, setStep] = useState<'options' | 'slots' | 'details'>('options');
  const [selectedOption, setSelectedOption] = useState<'same-time' | 'pick-slot' | null>(null);
  const [selectedSlot, setSelectedSlot] = useState<AvailableSlot | null>(null);
  const [meetingDetails, setMeetingDetails] = useState({
    title: '',
    description: '',
    duration: 30,
  });

  // Reset state when modal opens/closes
  useEffect(() => {
    if (open && parentMeeting) {
      setStep('options');
      setSelectedOption(null);
      setSelectedSlot(null);
      setMeetingDetails({
        title: `Follow-up: ${parentMeeting.title}`,
        description: `Follow-up meeting for: ${parentMeeting.title}`,
        duration: 30,
      });
    }
  }, [open, parentMeeting]);

  // Get participant emails
  const participantEmails = parentMeeting?.participants.map(p => p.email) || [];

  // Check availability query
  const { data: availabilityData, isLoading: isCheckingAvailability } = useQuery(
    ['availability', participantEmails, selectedOption],
    () => {
      if (!selectedOption || !parentMeeting) return null;
      
      const startDate = selectedOption === 'same-time' 
        ? addWeeks(new Date(parentMeeting.startTime), 1)
        : addDays(new Date(), 1);
      
      return meetingsApi.checkAvailability({
        emails: participantEmails,
        startDate: startDate.toISOString(),
        duration: meetingDetails.duration,
      });
    },
    {
      enabled: !!selectedOption && !!parentMeeting && participantEmails.length > 0,
    }
  );

  // Book meeting mutation
  const bookMeetingMutation = useMutation(
    (data: any) => meetingsApi.bookFollowUp(data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('meetings');
        toast.success('Follow-up meeting booked successfully!');
        onClose();
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.message || 'Failed to book meeting');
      },
    }
  );

  const handleOptionSelect = (option: 'same-time' | 'pick-slot') => {
    setSelectedOption(option);
    setStep('slots');
  };

  const handleSlotSelect = (slot: AvailableSlot) => {
    setSelectedSlot(slot);
    setStep('details');
  };

  const handleBookMeeting = () => {
    if (!parentMeeting || !selectedSlot) return;

    const bookingData = {
      parentMeetingId: parentMeeting._id,
      title: meetingDetails.title,
      description: meetingDetails.description,
      startTime: selectedSlot.start.toISOString(),
      endTime: selectedSlot.end.toISOString(),
      participants: parentMeeting.participants.map(p => ({
        email: p.email,
        name: p.name,
      })),
    };

    bookMeetingMutation.mutate(bookingData);
  };

  const getSameTimeNextWeek = () => {
    if (!parentMeeting) return null;
    const nextWeek = addWeeks(new Date(parentMeeting.startTime), 1);
    const endTime = addWeeks(new Date(parentMeeting.endTime), 1);
    return { start: nextWeek, end: endTime };
  };

  const renderOptionsStep = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Book your next meeting
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Great! "{parentMeeting?.title}" is complete. Let's schedule the follow-up.
      </Typography>

      <Grid container spacing={2}>
        {/* Same Time Next Week */}
        <Grid item xs={12} sm={6}>
          <Card
            sx={{
              cursor: 'pointer',
              border: '2px solid',
              borderColor: selectedOption === 'same-time' ? 'primary.main' : 'grey.200',
              '&:hover': { borderColor: 'primary.main' },
            }}
            onClick={() => handleOptionSelect('same-time')}
          >
            <CardContent sx={{ textAlign: 'center', py: 3 }}>
              <ScheduleIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Same Time Next Week
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {getSameTimeNextWeek() && format(getSameTimeNextWeek()!.start, 'EEEE, MMM d')}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {getSameTimeNextWeek() && 
                  `${format(getSameTimeNextWeek()!.start, 'h:mm a')} - ${format(getSameTimeNextWeek()!.end, 'h:mm a')}`
                }
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Pick a Slot */}
        <Grid item xs={12} sm={6}>
          <Card
            sx={{
              cursor: 'pointer',
              border: '2px solid',
              borderColor: selectedOption === 'pick-slot' ? 'primary.main' : 'grey.200',
              '&:hover': { borderColor: 'primary.main' },
            }}
            onClick={() => handleOptionSelect('pick-slot')}
          >
            <CardContent sx={{ textAlign: 'center', py: 3 }}>
              <CalendarIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Pick a Time Slot
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Choose from available times that work for everyone
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  const renderSlotsStep = () => {
    if (isCheckingAvailability) {
      return (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <CircularProgress />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Checking availability for all participants...
          </Typography>
        </Box>
      );
    }

    const availableSlots = availabilityData?.availableSlots || [];

    if (selectedOption === 'same-time') {
      const sameTimeSlot = getSameTimeNextWeek();
      if (sameTimeSlot) {
        // Check if same time is available
        const isAvailable = availableSlots.some(slot => 
          Math.abs(new Date(slot.start).getTime() - sameTimeSlot.start.getTime()) < 60000
        );

        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Same Time Next Week
            </Typography>
            <Card
              sx={{
                border: '2px solid',
                borderColor: isAvailable ? 'success.main' : 'error.main',
                mb: 2,
              }}
            >
              <CardContent>
                <Typography variant="subtitle1" fontWeight={600}>
                  {format(sameTimeSlot.start, 'EEEE, MMMM d')}
                </Typography>
                <Typography variant="body1">
                  {format(sameTimeSlot.start, 'h:mm a')} - {format(sameTimeSlot.end, 'h:mm a')}
                </Typography>
                {isAvailable ? (
                  <Alert severity="success" sx={{ mt: 2 }}>
                    ✅ This time works for everyone!
                  </Alert>
                ) : (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    ❌ This time conflicts with someone's schedule
                  </Alert>
                )}
              </CardContent>
            </Card>
            
            {isAvailable ? (
              <Button
                variant="contained"
                fullWidth
                onClick={() => handleSlotSelect(sameTimeSlot)}
              >
                Book This Time
              </Button>
            ) : (
              <Button
                variant="outlined"
                fullWidth
                onClick={() => setSelectedOption('pick-slot')}
              >
                Pick Different Time
              </Button>
            )}
          </Box>
        );
      }
    }

    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Available Time Slots
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          These times work for all {participantEmails.length} participants
        </Typography>

        {availableSlots.length === 0 ? (
          <Alert severity="warning">
            No available slots found. Try adjusting the time range or duration.
          </Alert>
        ) : (
          <Grid container spacing={2}>
            {availableSlots.slice(0, 12).map((slot, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    border: '1px solid',
                    borderColor: 'grey.200',
                    '&:hover': { borderColor: 'primary.main' },
                  }}
                  onClick={() => handleSlotSelect(slot)}
                >
                  <CardContent sx={{ textAlign: 'center', py: 2 }}>
                    <Typography variant="subtitle2" fontWeight={600}>
                      {format(new Date(slot.start), 'EEE, MMM d')}
                    </Typography>
                    <Typography variant="body2">
                      {format(new Date(slot.start), 'h:mm a')} - {format(new Date(slot.end), 'h:mm a')}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Box>
    );
  };

  const renderDetailsStep = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Meeting Details
      </Typography>

      {selectedSlot && (
        <Card sx={{ mb: 3, bgcolor: 'primary.50' }}>
          <CardContent>
            <Typography variant="subtitle1" fontWeight={600}>
              {format(new Date(selectedSlot.start), 'EEEE, MMMM d, yyyy')}
            </Typography>
            <Typography variant="body1">
              {format(new Date(selectedSlot.start), 'h:mm a')} - {format(new Date(selectedSlot.end), 'h:mm a')}
            </Typography>
          </CardContent>
        </Card>
      )}

      <Stack spacing={3}>
        <TextField
          label="Meeting Title"
          value={meetingDetails.title}
          onChange={(e) => setMeetingDetails(prev => ({ ...prev, title: e.target.value }))}
          fullWidth
          required
        />

        <TextField
          label="Description (Optional)"
          value={meetingDetails.description}
          onChange={(e) => setMeetingDetails(prev => ({ ...prev, description: e.target.value }))}
          multiline
          rows={3}
          fullWidth
        />

        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Participants
          </Typography>
          <Stack direction="row" spacing={1} flexWrap="wrap">
            {parentMeeting?.participants.map((participant, index) => (
              <Chip
                key={index}
                label={participant.name || participant.email}
                icon={<PersonIcon />}
                variant="outlined"
                size="small"
              />
            ))}
          </Stack>
        </Box>
      </Stack>
    </Box>
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 3 }
      }}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" fontWeight={600}>
          Book Next Meeting
        </Typography>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ py: 3 }}>
        {step === 'options' && renderOptionsStep()}
        {step === 'slots' && renderSlotsStep()}
        {step === 'details' && renderDetailsStep()}
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 3 }}>
        {step !== 'options' && (
          <Button
            onClick={() => {
              if (step === 'slots') setStep('options');
              if (step === 'details') setStep('slots');
            }}
          >
            Back
          </Button>
        )}

        <Box sx={{ flex: 1 }} />

        <Button onClick={onClose}>
          Cancel
        </Button>

        {step === 'details' && (
          <Button
            variant="contained"
            onClick={handleBookMeeting}
            disabled={!meetingDetails.title || bookMeetingMutation.isLoading}
          >
            {bookMeetingMutation.isLoading ? 'Booking...' : 'Book Meeting'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default NextMeetingModal;
